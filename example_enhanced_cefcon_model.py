"""
Enhanced CEFCON Model - 核心模块改进与性能提升
=====================================================

主要改进:
1. 多层次图注意力网络 (Hierarchical Graph Attention Network)
2. 自适应对比学习框架 (Adaptive Contrastive Learning)
3. 动态网络控制理论 (Dynamic Network Control Theory)
4. 多尺度特征融合 (Multi-scale Feature Fusion)
5. 生物学约束优化 (Biological Constraint Optimization)
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import GATConv, GCNConv, TransformerConv
from torch_geometric.utils import dropout_adj, to_dense_batch
import numpy as np
import pandas as pd
from sklearn.metrics import roc_auc_score, average_precision_score
import networkx as nx
from scipy.optimize import minimize
import cvxpy as cp
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# ========================================================================
# 1. 多层次图注意力网络模块
# ========================================================================

class HierarchicalGATLayer(nn.Module):
    """
    多层次图注意力网络层
    - 局部注意力: 捕获基因间直接调控关系
    - 全局注意力: 捕获远程调控和通路级别的影响
    - 跨层注意力: 整合不同层次的信息
    """
    def __init__(self, in_features: int, out_features: int, heads: int = 8, 
                 dropout: float = 0.2, alpha: float = 0.2):
        super(HierarchicalGATLayer, self).__init__()
        self.in_features = in_features
        self.out_features = out_features
        self.heads = heads
        self.dropout = dropout
        self.alpha = alpha
        
        # 局部注意力 - 用于邻居节点
        self.local_attention = GATConv(
            in_features, out_features // 2, heads=heads//2, dropout=dropout, 
            concat=True, add_self_loops=True
        )
        
        # 全局注意力 - 用于远程依赖
        self.global_attention = TransformerConv(
            in_features, out_features // 2, heads=heads//2, dropout=dropout,
            concat=True, beta=True
        )
        
        # 跨层融合注意力
        self.cross_layer_attention = nn.MultiheadAttention(
            embed_dim=out_features, num_heads=heads//2, dropout=dropout
        )
        
        # 特征融合层
        self.feature_fusion = nn.Sequential(
            nn.Linear(out_features, out_features),
            nn.LayerNorm(out_features),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # 残差连接
        self.residual_projection = nn.Linear(in_features, out_features) if in_features != out_features else nn.Identity()
        
    def forward(self, x: torch.Tensor, edge_index: torch.Tensor, 
                global_edge_index: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        前向传播
        Args:
            x: 节点特征 [N, in_features]
            edge_index: 局部边索引 [2, E_local]
            global_edge_index: 全局边索引 [2, E_global]
        Returns:
            输出特征 [N, out_features]
        """
        # 残差连接的输入
        residual = self.residual_projection(x)
        
        # 局部注意力
        local_out = self.local_attention(x, edge_index)
        
        # 全局注意力
        if global_edge_index is not None:
            global_out = self.global_attention(x, global_edge_index)
        else:
            # 如果没有全局边，使用完全连接图
            num_nodes = x.size(0)
            global_edge_index = torch.combinations(torch.arange(num_nodes), 2).t().contiguous()
            global_edge_index = torch.cat([global_edge_index, global_edge_index.flip(0)], dim=1)
            global_out = self.global_attention(x, global_edge_index.to(x.device))
        
        # 特征拼接
        combined_features = torch.cat([local_out, global_out], dim=1)
        
        # 跨层注意力融合
        combined_features = combined_features.unsqueeze(1)  # [N, 1, out_features]
        attended_features, _ = self.cross_layer_attention(
            combined_features, combined_features, combined_features
        )
        attended_features = attended_features.squeeze(1)  # [N, out_features]
        
        # 特征融合
        output = self.feature_fusion(attended_features)
        
        # 残差连接
        output = output + residual
        
        return output

class EnhancedGATNetwork(nn.Module):
    """
    增强的图注意力网络
    包含多个分层GAT层和特征融合机制
    """
    def __init__(self, input_dim: int, hidden_dims: List[int], output_dim: int,
                 num_heads: int = 8, dropout: float = 0.2, num_layers: int = 3):
        super(EnhancedGATNetwork, self).__init__()
        self.num_layers = num_layers
        self.dropout = dropout
        
        # 构建多层GAT
        self.gat_layers = nn.ModuleList()
        dims = [input_dim] + hidden_dims + [output_dim]
        
        for i in range(num_layers):
            self.gat_layers.append(
                HierarchicalGATLayer(
                    dims[i], dims[i+1], heads=num_heads, dropout=dropout
                )
            )
        
        # 层间特征融合
        self.layer_fusion = nn.ModuleList([
            nn.Linear(dims[i+1], output_dim) for i in range(num_layers)
        ])
        
        # 最终特征融合
        self.final_fusion = nn.Sequential(
            nn.Linear(output_dim * num_layers, output_dim),
            nn.LayerNorm(output_dim),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
    def forward(self, x: torch.Tensor, edge_index: torch.Tensor) -> torch.Tensor:
        layer_outputs = []
        
        for i, gat_layer in enumerate(self.gat_layers):
            x = gat_layer(x, edge_index)
            x = F.dropout(x, p=self.dropout, training=self.training)
            
            # 将每层输出投影到相同维度
            layer_output = self.layer_fusion[i](x)
            layer_outputs.append(layer_output)
        
        # 融合所有层的输出
        concatenated = torch.cat(layer_outputs, dim=1)
        final_output = self.final_fusion(concatenated)
        
        return final_output

# ========================================================================
# 2. 自适应对比学习框架
# ========================================================================

class AdaptiveContrastiveLearning(nn.Module):
    """
    自适应对比学习框架
    - 动态负样本挖掘
    - 难样本加权
    - 多尺度对比
    """
    def __init__(self, embedding_dim: int, temperature: float = 0.07,
                 negative_sampling_ratio: float = 10.0):
        super(AdaptiveContrastiveLearning, self).__init__()
        self.embedding_dim = embedding_dim
        self.temperature = temperature
        self.negative_sampling_ratio = negative_sampling_ratio
        
        # 投影头
        self.projection_head = nn.Sequential(
            nn.Linear(embedding_dim, embedding_dim),
            nn.ReLU(),
            nn.Linear(embedding_dim, embedding_dim // 2)
        )
        
        # 动态温度参数
        self.adaptive_temperature = nn.Parameter(torch.tensor(temperature))
        
        # 难样本权重网络
        self.difficulty_scorer = nn.Sequential(
            nn.Linear(embedding_dim * 2, embedding_dim),
            nn.ReLU(),
            nn.Linear(embedding_dim, 1),
            nn.Sigmoid()
        )
        
    def forward(self, embeddings: torch.Tensor, positive_pairs: torch.Tensor,
                negative_pairs: torch.Tensor) -> torch.Tensor:
        """
        自适应对比学习损失
        Args:
            embeddings: 节点嵌入 [N, embedding_dim]
            positive_pairs: 正样本对 [P, 2]
            negative_pairs: 负样本对 [N, 2]
        """
        # 投影到对比学习空间
        projected_embeddings = self.projection_head(embeddings)
        projected_embeddings = F.normalize(projected_embeddings, dim=1)
        
        # 计算正样本相似度
        pos_similarities = self._compute_similarity(
            projected_embeddings, positive_pairs
        )
        
        # 计算负样本相似度
        neg_similarities = self._compute_similarity(
            projected_embeddings, negative_pairs
        )
        
        # 难样本加权
        pos_weights = self._compute_difficulty_weights(embeddings, positive_pairs)
        neg_weights = self._compute_difficulty_weights(embeddings, negative_pairs)
        
        # 自适应温度的InfoNCE损失
        pos_exp = torch.exp(pos_similarities / self.adaptive_temperature)
        neg_exp = torch.exp(neg_similarities / self.adaptive_temperature)
        
        # 加权对比损失
        numerator = pos_weights * pos_exp
        denominator = numerator + torch.sum(neg_weights * neg_exp, dim=1, keepdim=True)
        
        loss = -torch.log(numerator / (denominator + 1e-8))
        
        return loss.mean()
    
    def _compute_similarity(self, embeddings: torch.Tensor, 
                          pairs: torch.Tensor) -> torch.Tensor:
        """计算对的相似度"""
        emb1 = embeddings[pairs[:, 0]]
        emb2 = embeddings[pairs[:, 1]]
        return torch.sum(emb1 * emb2, dim=1)
    
    def _compute_difficulty_weights(self, embeddings: torch.Tensor,
                                  pairs: torch.Tensor) -> torch.Tensor:
        """计算难样本权重"""
        emb1 = embeddings[pairs[:, 0]]
        emb2 = embeddings[pairs[:, 1]]
        pair_features = torch.cat([emb1, emb2], dim=1)
        weights = self.difficulty_scorer(pair_features).squeeze()
        return weights

# ========================================================================
# 3. 动态网络控制理论模块
# ========================================================================

class DynamicNetworkControl:
    """
    动态网络控制理论
    - 时变控制能力评估
    - 多目标优化驱动因子识别
    - 稳定性分析
    """
    def __init__(self, alpha: float = 0.1, beta: float = 0.1):
        self.alpha = alpha  # 控制成本权重
        self.beta = beta    # 稳定性权重
        
    def compute_dynamic_controllability(self, adjacency_matrix: np.ndarray,
                                      time_steps: int = 10) -> Dict[str, np.ndarray]:
        """
        计算动态可控性指标
        """
        n_nodes = adjacency_matrix.shape[0]
        controllability_scores = np.zeros((n_nodes, time_steps))
        
        for t in range(time_steps):
            # 时变邻接矩阵 (可以根据实际情况调整)
            A_t = adjacency_matrix * (1 - 0.1 * t / time_steps)
            
            # 计算可控性格拉米安矩阵
            controllability_gramian = self._compute_controllability_gramian(A_t)
            
            # 计算每个节点的可控性
            for i in range(n_nodes):
                B_i = np.zeros((n_nodes, 1))
                B_i[i, 0] = 1
                controllability_scores[i, t] = np.trace(
                    B_i.T @ controllability_gramian @ B_i
                )
        
        return {
            'controllability_scores': controllability_scores,
            'mean_controllability': np.mean(controllability_scores, axis=1),
            'std_controllability': np.std(controllability_scores, axis=1)
        }
    
    def _compute_controllability_gramian(self, A: np.ndarray,
                                       time_horizon: float = 10.0) -> np.ndarray:
        """计算可控性格拉米安矩阵"""
        n = A.shape[0]
        try:
            # 使用矩阵指数计算
            from scipy.linalg import expm, solve_continuous_lyapunov
            
            # 稳定性检查
            eigenvals = np.linalg.eigvals(A)
            if np.any(np.real(eigenvals) >= 0):
                # 如果系统不稳定，添加阻尼
                A = A - 0.1 * np.eye(n)
            
            # 解李雅普诺夫方程: A @ W + W @ A.T + B @ B.T = 0
            B = np.eye(n)  # 假设每个节点都可以被控制
            W = solve_continuous_lyapunov(A, -B @ B.T)
            
            return W
        except:
            # 如果求解失败，返回单位矩阵
            return np.eye(n)
    
    def identify_multi_objective_drivers(self, adjacency_matrix: np.ndarray,
                                       expression_data: np.ndarray,
                                       target_nodes: List[int],
                                       num_drivers: int = 10) -> Dict[str, any]:
        """
        多目标优化驱动因子识别
        目标:
        1. 最大化可控性
        2. 最小化控制成本
        3. 最大化目标节点的影响
        """
        n_nodes = adjacency_matrix.shape[0]
        
        # 计算可控性分数
        controllability_results = self.compute_dynamic_controllability(adjacency_matrix)
        controllability_scores = controllability_results['mean_controllability']
        
        # 计算到目标节点的影响
        target_influence = self._compute_target_influence(
            adjacency_matrix, target_nodes
        )
        
        # 计算表达变异性 (作为控制成本的代理)
        expression_variance = np.var(expression_data, axis=0)
        
        # 多目标优化
        def objective_function(x):
            """
            x: 二进制变量，表示是否选择该基因作为驱动因子
            """
            selected_indices = np.where(x > 0.5)[0]
            
            if len(selected_indices) == 0:
                return float('inf')
            
            # 目标1: 最大化可控性 (最小化负可控性)
            controllability_obj = -np.sum(controllability_scores[selected_indices])
            
            # 目标2: 最小化控制成本
            cost_obj = np.sum(expression_variance[selected_indices])
            
            # 目标3: 最大化目标影响 (最小化负影响)
            influence_obj = -np.sum(target_influence[selected_indices])
            
            # 约束: 驱动因子数量
            num_selected = len(selected_indices)
            penalty = abs(num_selected - num_drivers) * 1000
            
            # 加权组合
            total_obj = (controllability_obj + 
                        self.alpha * cost_obj + 
                        self.beta * influence_obj + 
                        penalty)
            
            return total_obj
        
        # 使用遗传算法或其他优化方法
        from scipy.optimize import differential_evolution
        
        bounds = [(0, 1) for _ in range(n_nodes)]
        result = differential_evolution(
            objective_function,
            bounds,
            maxiter=1000,
            popsize=50,
            seed=42
        )
        
        # 获取最优解
        optimal_x = result.x
        driver_indices = np.argsort(optimal_x)[-num_drivers:]
        
        return {
            'driver_indices': driver_indices,
            'driver_scores': optimal_x[driver_indices],
            'controllability_scores': controllability_scores[driver_indices],
            'target_influence': target_influence[driver_indices],
            'optimization_result': result
        }
    
    def _compute_target_influence(self, adjacency_matrix: np.ndarray,
                                target_nodes: List[int]) -> np.ndarray:
        """计算对目标节点的影响"""
        n_nodes = adjacency_matrix.shape[0]
        influence_scores = np.zeros(n_nodes)
        
        # 使用随机游走或网络传播模型
        for target in target_nodes:
            # 从目标节点反向传播
            influence = self._random_walk_influence(adjacency_matrix, target)
            influence_scores += influence
            
        return influence_scores / len(target_nodes)
    
    def _random_walk_influence(self, adjacency_matrix: np.ndarray,
                             target_node: int, restart_prob: float = 0.15) -> np.ndarray:
        """使用随机游走计算影响分数"""
        n_nodes = adjacency_matrix.shape[0]
        
        # 转置邻接矩阵以进行反向传播
        A_T = adjacency_matrix.T
        
        # 行归一化
        row_sums = np.sum(A_T, axis=1)
        row_sums[row_sums == 0] = 1  # 避免除零
        transition_matrix = A_T / row_sums[:, np.newaxis]
        
        # 初始化
        current_prob = np.zeros(n_nodes)
        current_prob[target_node] = 1.0
        
        # 迭代随机游走
        for _ in range(100):  # 迭代次数
            new_prob = (1 - restart_prob) * (transition_matrix @ current_prob)
            new_prob[target_node] += restart_prob
            
            # 检查收敛
            if np.allclose(current_prob, new_prob, atol=1e-6):
                break
                
            current_prob = new_prob
        
        return current_prob

# ========================================================================
# 4. 多尺度特征融合模块
# ========================================================================

class MultiScaleFeatureFusion(nn.Module):
    """
    多尺度特征融合模块
    - 基因级别特征
    - 通路级别特征
    - 细胞类型级别特征
    """
    def __init__(self, gene_dim: int, pathway_dim: int, celltype_dim: int,
                 fusion_dim: int = 256):
        super(MultiScaleFeatureFusion, self).__init__()
        self.gene_dim = gene_dim
        self.pathway_dim = pathway_dim
        self.celltype_dim = celltype_dim
        self.fusion_dim = fusion_dim
        
        # 各尺度特征编码器
        self.gene_encoder = nn.Sequential(
            nn.Linear(gene_dim, fusion_dim),
            nn.LayerNorm(fusion_dim),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        self.pathway_encoder = nn.Sequential(
            nn.Linear(pathway_dim, fusion_dim),
            nn.LayerNorm(fusion_dim),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        self.celltype_encoder = nn.Sequential(
            nn.Linear(celltype_dim, fusion_dim),
            nn.LayerNorm(fusion_dim),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        # 跨尺度注意力机制
        self.cross_scale_attention = nn.MultiheadAttention(
            embed_dim=fusion_dim, num_heads=8, dropout=0.1
        )
        
        # 尺度权重学习
        self.scale_weights = nn.Parameter(torch.ones(3) / 3)
        
        # 最终融合层
        self.fusion_layer = nn.Sequential(
            nn.Linear(fusion_dim * 3, fusion_dim),
            nn.LayerNorm(fusion_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(fusion_dim, fusion_dim)
        )
        
    def forward(self, gene_features: torch.Tensor,
                pathway_features: torch.Tensor,
                celltype_features: torch.Tensor) -> torch.Tensor:
        """
        多尺度特征融合
        """
        # 编码各尺度特征
        gene_encoded = self.gene_encoder(gene_features)
        pathway_encoded = self.pathway_encoder(pathway_features)
        celltype_encoded = self.celltype_encoder(celltype_features)
        
        # 堆叠特征用于注意力机制
        stacked_features = torch.stack([
            gene_encoded, pathway_encoded, celltype_encoded
        ], dim=0)  # [3, N, fusion_dim]
        
        # 跨尺度注意力
        attended_features, attention_weights = self.cross_scale_attention(
            stacked_features, stacked_features, stacked_features
        )
        
        # 加权平均
        weighted_features = []
        for i in range(3):
            weighted_features.append(
                self.scale_weights[i] * attended_features[i]
            )
        
        # 拼接所有尺度特征
        concatenated = torch.cat(weighted_features, dim=1)
        
        # 最终融合
        fused_features = self.fusion_layer(concatenated)
        
        return fused_features, attention_weights

# ========================================================================
# 5. 生物学约束优化模块
# ========================================================================

class BiologicalConstraintOptimization:
    """
    生物学约束优化模块
    - 整合生物学先验知识
    - 约束网络结构
    - 确保生物学合理性
    """
    def __init__(self, gene_ontology: Dict[str, List[str]] = None,
                 pathway_database: Dict[str, List[str]] = None,
                 protein_interactions: List[Tuple[str, str]] = None):
        self.gene_ontology = gene_ontology or {}
        self.pathway_database = pathway_database or {}
        self.protein_interactions = protein_interactions or []
        
        # 构建生物学约束图
        self.constraint_graph = self._build_constraint_graph()
        
    def _build_constraint_graph(self) -> nx.Graph:
        """构建生物学约束图"""
        G = nx.Graph()
        
        # 添加蛋白质相互作用边
        for protein1, protein2 in self.protein_interactions:
            G.add_edge(protein1, protein2, type='PPI', weight=1.0)
        
        # 添加GO术语相关性
        for go_term, genes in self.gene_ontology.items():
            for i, gene1 in enumerate(genes):
                for gene2 in genes[i+1:]:
                    if not G.has_edge(gene1, gene2):
                        G.add_edge(gene1, gene2, type='GO', weight=0.5)
        
        # 添加通路相关性
        for pathway, genes in self.pathway_database.items():
            for i, gene1 in enumerate(genes):
                for gene2 in genes[i+1:]:
                    if G.has_edge(gene1, gene2):
                        G[gene1][gene2]['weight'] += 0.5
                    else:
                        G.add_edge(gene1, gene2, type='Pathway', weight=0.5)
        
        return G
    
    def apply_biological_constraints(self, predicted_network: np.ndarray,
                                   gene_names: List[str],
                                   constraint_strength: float = 0.5) -> np.ndarray:
        """
        应用生物学约束到预测网络
        """
        constrained_network = predicted_network.copy()
        n_genes = len(gene_names)
        
        # 创建基因名到索引的映射
        gene_to_idx = {gene: idx for idx, gene in enumerate(gene_names)}
        
        # 应用正约束 (增强已知相互作用)
        for gene1, gene2, data in self.constraint_graph.edges(data=True):
            if gene1 in gene_to_idx and gene2 in gene_to_idx:
                idx1, idx2 = gene_to_idx[gene1], gene_to_idx[gene2]
                constraint_weight = data.get('weight', 1.0)
                
                # 增强预测的边权重
                enhancement = constraint_strength * constraint_weight
                constrained_network[idx1, idx2] += enhancement
                constrained_network[idx2, idx1] += enhancement
        
        # 应用负约束 (抑制不太可能的相互作用)
        # 这里可以根据具体需求实现
        
        # 确保网络的稀疏性
        constrained_network = self._ensure_sparsity(
            constrained_network, sparsity_level=0.1
        )
        
        return constrained_network
    
    def _ensure_sparsity(self, network: np.ndarray,
                        sparsity_level: float = 0.1) -> np.ndarray:
        """确保网络稀疏性"""
        # 计算需要保留的边数
        total_edges = network.shape[0] * (network.shape[0] - 1) // 2
        edges_to_keep = int(total_edges * sparsity_level)
        
        # 获取上三角矩阵的值和索引
        triu_indices = np.triu_indices(network.shape[0], k=1)
        edge_weights = network[triu_indices]
        
        # 选择权重最大的边
        top_indices = np.argsort(edge_weights)[-edges_to_keep:]
        
        # 创建稀疏网络
        sparse_network = np.zeros_like(network)
        selected_i = triu_indices[0][top_indices]
        selected_j = triu_indices[1][top_indices]
        
        sparse_network[selected_i, selected_j] = edge_weights[top_indices]
        sparse_network[selected_j, selected_i] = edge_weights[top_indices]
        
        return sparse_network

# ========================================================================
# 6. 整合的Enhanced CEFCON模型
# ========================================================================

class EnhancedCEFCONModel(nn.Module):
    """
    增强的CEFCON模型
    整合所有改进模块
    """
    def __init__(self, config: Dict):
        super(EnhancedCEFCONModel, self).__init__()
        self.config = config
        
        # 提取配置参数
        self.input_dim = config['input_dim']
        self.hidden_dims = config['hidden_dims']
        self.output_dim = config['output_dim']
        self.num_heads = config.get('num_heads', 8)
        self.dropout = config.get('dropout', 0.2)
        self.temperature = config.get('temperature', 0.07)
        
        # 核心组件
        self.enhanced_gat = EnhancedGATNetwork(
            self.input_dim, self.hidden_dims, self.output_dim,
            self.num_heads, self.dropout
        )
        
        self.contrastive_learner = AdaptiveContrastiveLearning(
            self.output_dim, self.temperature
        )
        
        self.multiscale_fusion = MultiScaleFeatureFusion(
            gene_dim=self.output_dim,
            pathway_dim=config.get('pathway_dim', 50),
            celltype_dim=config.get('celltype_dim', 10),
            fusion_dim=self.output_dim
        )
        
        self.network_controller = DynamicNetworkControl()
        self.bio_constraint_optimizer = BiologicalConstraintOptimization()
        
        # 网络重构头
        self.network_reconstruction = nn.Sequential(
            nn.Linear(self.output_dim, self.output_dim),
            nn.ReLU(),
            nn.Linear(self.output_dim, 1),
            nn.Sigmoid()
        )
        
    def forward(self, x: torch.Tensor, edge_index: torch.Tensor,
                pathway_features: torch.Tensor = None,
                celltype_features: torch.Tensor = None,
                positive_pairs: torch.Tensor = None,
                negative_pairs: torch.Tensor = None) -> Dict[str, torch.Tensor]:
        """
        前向传播
        """
        # 1. 通过增强GAT获得基因嵌入
        gene_embeddings = self.enhanced_gat(x, edge_index)
        
        # 2.