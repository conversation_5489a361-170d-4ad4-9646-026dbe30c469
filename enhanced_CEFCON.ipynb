{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["'/mnt/f/文献汇报/8_scNET/temporal_scRNA_related/2_CEFCON/code'"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["pwd"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from example_enhanced_cefcon_model import run_enhanced_cefcon_pipeline, generate_sample_data"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# 生成示例数据\n", "expression_data, prior_network, gene_de_scores = generate_sample_data()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 运行完整流水线\n", "results = run_enhanced_cefcon_pipeline(\n", "    expression_data=expression_data,\n", "    prior_network=prior_network,\n", "    gene_de_scores=gene_de_scores\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "CEFCON", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.17"}}, "nbformat": 4, "nbformat_minor": 2}